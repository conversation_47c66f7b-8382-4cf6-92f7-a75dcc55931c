/**
 * Structured logging service for the MCP server
 */

export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug'
}

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  service: string;
  [key: string]: any;
}

interface ScrapingLogData {
  asin?: string;
  topic?: string;
  url?: string;
  statusCode?: number;
  userAgent?: string;
  proxyUrl?: string;
  responseTime?: number;
  error?: string;
}

class Logger {
  private serviceName: string = 'mcp-server';
  private logLevel: LogLevel;

  constructor() {
    // Set log level from environment or default to INFO
    const envLogLevel = process.env.LOG_LEVEL?.toLowerCase();
    this.logLevel = this.isValidLogLevel(envLogLevel) ? envLogLevel as LogLevel : LogLevel.INFO;
  }

  private isValidLogLevel(level: string | undefined): boolean {
    return level !== undefined && Object.values(LogLevel).includes(level as LogLevel);
  }

  private shouldLog(level: LogLevel): boolean {
    const levels = [LogLevel.ERROR, LogLevel.WARN, LogLevel.INFO, LogLevel.DEBUG];
    const currentLevelIndex = levels.indexOf(this.logLevel);
    const messageLevelIndex = levels.indexOf(level);
    return messageLevelIndex <= currentLevelIndex;
  }

  private createLogEntry(level: LogLevel, message: string, data?: Record<string, any>): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      service: this.serviceName,
      ...data
    };
  }

  private output(logEntry: LogEntry): void {
    if (!this.shouldLog(logEntry.level)) {
      return;
    }

    // For MCP servers, we need to avoid stdout/stderr output that interferes with stdio communication
    // Only log to stderr for errors, and only in non-MCP mode
    const isMcpMode = process.env.NODE_ENV === 'production' || process.argv.includes('mcp-server.js');

    if (isMcpMode) {
      // In MCP mode, suppress all logging to avoid interfering with stdio protocol
      return;
    }

    const logFormat = process.env.LOG_FORMAT?.toLowerCase();

    if (logFormat === 'json') {
      console.error(JSON.stringify(logEntry)); // Use stderr instead of stdout
    } else {
      // Human-readable format for development
      const { timestamp, level, message, service, ...rest } = logEntry;
      const restStr = Object.keys(rest).length > 0 ? ` ${JSON.stringify(rest)}` : '';
      console.error(`[${timestamp}] ${level.toUpperCase()} [${service}] ${message}${restStr}`); // Use stderr instead of stdout
    }
  }

  error(message: string, data?: Record<string, any>): void {
    this.output(this.createLogEntry(LogLevel.ERROR, message, data));
  }

  warn(message: string, data?: Record<string, any>): void {
    this.output(this.createLogEntry(LogLevel.WARN, message, data));
  }

  info(message: string, data?: Record<string, any>): void {
    this.output(this.createLogEntry(LogLevel.INFO, message, data));
  }

  debug(message: string, data?: Record<string, any>): void {
    this.output(this.createLogEntry(LogLevel.DEBUG, message, data));
  }

  // Specialized logging methods for common scenarios
  logRequest(method: string, path: string, data?: Record<string, any>): void {
    this.info('HTTP Request', {
      method,
      path,
      ...data
    });
  }

  logResponse(method: string, path: string, statusCode: number, responseTime: number, data?: Record<string, any>): void {
    this.info('HTTP Response', {
      method,
      path,
      statusCode,
      responseTime,
      ...data
    });
  }

  logScrapingAttempt(data: ScrapingLogData): void {
    this.info('Scraping Attempt', data);
  }

  logScrapingSuccess(data: ScrapingLogData): void {
    this.info('Scraping Success', data);
  }

  logScrapingFailure(data: ScrapingLogData): void {
    this.error('Scraping Failure', data);
  }

  logProxyRotation(proxyUrl: string, data?: Record<string, any>): void {
    this.debug('Proxy Rotation', {
      proxyUrl,
      ...data
    });
  }

  logUserAgentRotation(userAgent: string, data?: Record<string, any>): void {
    this.debug('User Agent Rotation', {
      userAgent,
      ...data
    });
  }

  logDatabaseOperation(operation: string, table: string, data?: Record<string, any>): void {
    this.debug('Database Operation', {
      operation,
      table,
      ...data
    });
  }

  logValidationError(endpoint: string, errors: any[], data?: Record<string, any>): void {
    this.warn('Validation Error', {
      endpoint,
      errors,
      ...data
    });
  }
}

// Export singleton instance
export const logger = new Logger();
export default logger;
