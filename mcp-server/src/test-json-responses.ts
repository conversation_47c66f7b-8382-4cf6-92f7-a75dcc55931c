#!/usr/bin/env node

/**
 * Test script to validate JSON response formats for MCP tools
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

// Mock data for testing
const mockProductSearchResult = {
  success: true,
  searchTerm: "laptop",
  totalFound: 2,
  products: [
    { asin: "B08N5WRWNW", position: 1 },
    { asin: "B08N5WRXYZ", position: 2 },
  ],
  message: 'Found 2 products for "laptop". Use the "get-product-details" tool with any of these ASINs to get detailed product information.',
};

const mockProductDetailsResult = {
  success: true,
  asin: "B08N5WRWNW",
  product: {
    title: "Test Laptop",
    price: {
      amount: 999.99,
      currency: "S$",
      formatted: "S$ 999.99",
    },
    rating: {
      value: "4.5",
      formatted: "4.5/5",
    },
    reviews: {
      count: "1,234",
      formatted: "1,234",
    },
    images: {
      main: "https://example.com/image.jpg",
    },
    features: ["Feature 1", "Feature 2"],
    url: "https://amazon.sg/dp/B08N5WRWNW",
  },
};

const mockImageFetchResult = {
  success: true,
  asin: "B08N5WRWNW",
  imageType: "main",
  message: "Successfully fetched main image for ASIN B08N5WRWNW",
};

const mockErrorResponse = {
  success: false,
  error: "Test error message",
  details: {
    asin: "B08N5WRWNW",
    statusCode: 404,
  },
};

/**
 * Validates that a response is valid JSON
 */
function validateJsonResponse(response: any, testName: string): boolean {
  try {
    // Check if response has the required MCP structure
    if (!response.content || !Array.isArray(response.content)) {
      console.error(`❌ ${testName}: Missing or invalid content array`);
      return false;
    }

    // Check each content item
    for (const item of response.content) {
      if (!item.type) {
        console.error(`❌ ${testName}: Content item missing type`);
        return false;
      }

      if (item.type === "text" && !item.text) {
        console.error(`❌ ${testName}: Text content item missing text`);
        return false;
      }

      if (item.type === "image" && (!item.data || !item.mimeType)) {
        console.error(`❌ ${testName}: Image content item missing data or mimeType`);
        return false;
      }

      // For text content, try to parse as JSON
      if (item.type === "text") {
        try {
          const parsedData = JSON.parse(item.text);
          
          // Check if it has a success field
          if (typeof parsedData.success !== "boolean") {
            console.error(`❌ ${testName}: Parsed JSON missing success field`);
            return false;
          }

          console.log(`✅ ${testName}: Valid JSON response structure`);
          console.log(`   Data preview: ${JSON.stringify(parsedData, null, 2).substring(0, 200)}...`);
          return true;
        } catch (parseError) {
          console.error(`❌ ${testName}: Invalid JSON in text content: ${parseError}`);
          return false;
        }
      }
    }

    return true;
  } catch (error) {
    console.error(`❌ ${testName}: Validation error: ${error}`);
    return false;
  }
}

/**
 * Creates mock responses for testing
 */
function createMockResponse(data: any) {
  return {
    content: [
      {
        type: "text" as const,
        text: JSON.stringify(data, null, 2),
      },
    ],
  };
}

function createMockErrorResponse(error: string, details?: any) {
  const errorData = {
    success: false,
    error,
    ...(details && { details }),
  };

  return {
    content: [
      {
        type: "text" as const,
        text: JSON.stringify(errorData, null, 2),
      },
    ],
    isError: true,
  };
}

function createMockImageResponse(data: any) {
  return {
    content: [
      {
        type: "text" as const,
        text: JSON.stringify(data, null, 2),
      },
      {
        type: "image" as const,
        data: "base64encodedimagedata",
        mimeType: "image/jpeg",
      },
    ],
  };
}

/**
 * Run validation tests
 */
function runTests() {
  console.log("🧪 Testing MCP JSON Response Formats\n");

  let passedTests = 0;
  let totalTests = 0;

  // Test 1: Product Search Success Response
  totalTests++;
  const searchResponse = createMockResponse(mockProductSearchResult);
  if (validateJsonResponse(searchResponse, "Product Search Success")) {
    passedTests++;
  }

  // Test 2: Product Details Success Response
  totalTests++;
  const detailsResponse = createMockResponse(mockProductDetailsResult);
  if (validateJsonResponse(detailsResponse, "Product Details Success")) {
    passedTests++;
  }

  // Test 3: Image Fetch Success Response
  totalTests++;
  const imageResponse = createMockImageResponse(mockImageFetchResult);
  if (validateJsonResponse(imageResponse, "Image Fetch Success")) {
    passedTests++;
  }

  // Test 4: Error Response
  totalTests++;
  const errorResponse = createMockErrorResponse("Test error message", { asin: "B08N5WRWNW" });
  if (validateJsonResponse(errorResponse, "Error Response")) {
    passedTests++;
  }

  // Test 5: Resource Response (Search History)
  totalTests++;
  const searchHistoryResponse = {
    contents: [
      {
        uri: "search://history",
        text: JSON.stringify({
          success: true,
          totalSearches: 1,
          searches: [
            {
              id: "test-id",
              topic: "laptop",
              limit: 10,
              productCount: 2,
              createdAt: new Date().toISOString(),
              products: ["B08N5WRWNW", "B08N5WRXYZ"],
            },
          ],
        }, null, 2),
        mimeType: "application/json",
      },
    ],
  };

  // Validate resource response structure
  try {
    if (searchHistoryResponse.contents && Array.isArray(searchHistoryResponse.contents)) {
      const content = searchHistoryResponse.contents[0];
      if (content.text) {
        const parsedData = JSON.parse(content.text);
        if (typeof parsedData.success === "boolean") {
          console.log("✅ Search History Resource: Valid JSON response structure");
          passedTests++;
        } else {
          console.error("❌ Search History Resource: Missing success field");
        }
      } else {
        console.error("❌ Search History Resource: Missing text content");
      }
    } else {
      console.error("❌ Search History Resource: Invalid contents structure");
    }
  } catch (error) {
    console.error(`❌ Search History Resource: Validation error: ${error}`);
  }

  console.log(`\n📊 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log("🎉 All tests passed! JSON response formats are valid.");
    process.exit(0);
  } else {
    console.log("❌ Some tests failed. Please check the response formats.");
    process.exit(1);
  }
}

// Run the tests
if (require.main === module) {
  runTests();
}

export { validateJsonResponse, runTests };
