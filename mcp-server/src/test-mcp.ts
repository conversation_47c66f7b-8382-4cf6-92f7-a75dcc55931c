#!/usr/bin/env node

/**
 * Simple MCP Server Test
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z } from "zod";

// Create MCP server instance
const server = new McpServer({
  name: "test-mcp",
  version: "1.0.0"
});

// Simple tool without complex schemas
server.tool(
  "hello",
  "Say hello",
  {
    name: z.string()
  },
  {},
  async ({ name }) => {
    return {
      content: [{
        type: "text",
        text: `Hello, ${name}!`
      }]
    };
  }
);

/**
 * Main function to start the MCP server
 */
async function main() {
  try {
    // Connect to stdio transport for MCP communication
    const transport = new StdioServerTransport();
    await server.connect(transport);
    
    console.error('Test MCP Server started successfully');
  } catch (error) {
    console.error('Failed to start MCP server:', error);
    process.exit(1);
  }
}

// Start the server
if (require.main === module) {
  main().catch((error) => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });
}

export default server;
