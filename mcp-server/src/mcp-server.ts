#!/usr/bin/env node

/**
 * MCP Server for Amazon Product Scraping
 *
 * This server exposes Amazon product scraping functionality through the Model Context Protocol.
 * It provides tools for searching products, fetching product details, and retrieving images.
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { z, ZodObject } from "zod";
import * as cheerio from "cheerio";
import axios from "axios";
import prisma from "./lib/prisma.js";
import userAgentService from "./services/user-agent.js";
import proxyService from "./services/proxy.js";
import logger from "./services/logger.js";

// Response helper functions for consistent JSON formatting
interface ProductSearchResult {
  success: boolean;
  searchTerm: string;
  totalFound: number;
  products: Array<{
    asin: string;
    position: number;
  }>;
  message?: string;
}

interface ProductDetailsResult {
  success: boolean;
  asin: string;
  product?: {
    title: string;
    price: {
      amount: number | null;
      currency: string;
      formatted: string;
    };
    rating: {
      value: string;
      formatted: string;
    };
    reviews: {
      count: string;
      formatted: string;
    };
    images: {
      main: string;
    };
    features: string[];
    url: string;
  };
  error?: string;
}

interface ImageFetchResult {
  success: boolean;
  asin: string;
  imageType: string;
  message?: string;
  error?: string;
}

/**
 * Creates a standardized success response for tools
 */
function createSuccessResponse(data: any) {
  return {
    content: [
      {
        properties: data,
      },
    ],
  };
}

/**
 * Creates a standardized error response for tools
 */
function createErrorResponse(error: string, details?: any) {
  const errorData = {
    success: false,
    error,
    ...(details && { details }),
  };

  return {
    content: [
      {
        type: "text" as const,
        text: JSON.stringify(errorData, null, 2),
      },
    ],
    isError: true,
  };
}

// Amazon base URL
const AMAZON_BASE_URL = "https://www.amazon.sg";

// Create MCP server instance
const server = new McpServer({
  name: "amazon-scraper-mcp",
  version: "1.0.0",
});

/**
 * Tool: Search Amazon Products
 * Searches for products on Amazon.sg and returns product ASINs
 *
 * Output Format:
 * {
 *   "success": boolean,
 *   "searchTerm": string,
 *   "totalFound": number,
 *   "products": Array<{
 *     "asin": string,
 *     "position": number
 *   }>,
 *   "message"?: string
 * }
 */
server.registerTool(
  "search-amazon-products",
  {
    title: "Search Amazon Products",
    description:
      "Search for products on Amazon.sg and return product identifiers (ASINs). Returns a JSON object with success status, search term, total found count, and array of products with ASINs and positions.",
    inputSchema: {
      topic: z.string().describe("Search query/topic for products"),
      limit: z
        .number()
        .min(1)
        .max(50)
        .default(10)
        .describe("Maximum number of products to return (1-50)"),
    },
    outputSchema: {
      properties: z.object({
        success: z.boolean(),
        searchTerm: z.string(),
        totalFound: z.number(),
        products: z.array(
          z.object({
            asin: z.string(),
            position: z.number(),
          })
        ),
        message: z.string().optional(),
      }),
    },
  },
  async ({ topic, limit = 10 }) => {
    try {
      logger.logScrapingAttempt({
        topic,
        url: `${AMAZON_BASE_URL}/s?k=${encodeURIComponent(topic)}`,
      });

      // Create search URL
      const searchUrl = `${AMAZON_BASE_URL}/s?k=${encodeURIComponent(topic)}`;

      // Get user agent and proxy configuration
      const userAgent = userAgentService.getRotatedUserAgent();
      const proxyConfig = proxyService.isEnabled()
        ? await proxyService.getProxyConfig()
        : null;

      // Make request to Amazon.sg
      const axiosConfig: any = {
        headers: {
          "User-Agent": userAgent,
        },
        timeout: 30000,
      };

      if (proxyConfig) {
        axiosConfig.proxy = proxyConfig;
      }

      const response = await axios.get<string>(searchUrl, axiosConfig);

      logger.logScrapingSuccess({
        topic,
        url: searchUrl,
        statusCode: response.status,
        responseTime: Date.now(),
        userAgent,
        proxyUrl: proxyConfig?.host,
      });

      // Parse HTML with Cheerio
      const $ = cheerio.load(response.data);

      // Extract product ASINs from the page
      const productIdentifiers: { asin: string }[] = [];

      // Look for elements with data-asin attribute (Amazon's product containers)
      $("[role=listitem][data-asin]").each((_index, element) => {
        const asin = $(element).attr("data-asin");

        // Only add valid ASINs and respect the limit
        if (asin && asin.trim() !== "" && productIdentifiers.length < limit) {
          productIdentifiers.push({ asin });
        }
      });

      // Save search query to database
      const productPromises = productIdentifiers.map(({ asin }) =>
        prisma.product.upsert({
          where: { asin },
          update: {},
          create: { asin },
        })
      );

      await Promise.all(productPromises);

      // Create search query record
      await prisma.searchQuery.create({
        data: {
          topic,
          limit,
          products: {
            connect: productIdentifiers.map(({ asin }) => ({ asin })),
          },
        },
      });

      const searchResult: ProductSearchResult = {
        success: true,
        searchTerm: topic,
        totalFound: productIdentifiers.length,
        products: productIdentifiers.map((p, i) => ({
          asin: p.asin,
          position: i + 1,
        })),
        message: `Found ${productIdentifiers.length} products for "${topic}". Use the "get-product-details" tool with any of these ASINs to get detailed product information.`,
      };

      return createSuccessResponse(searchResult);
    } catch (error: any) {
      logger.logScrapingFailure({
        topic,
        url: `${AMAZON_BASE_URL}/s?k=${encodeURIComponent(topic)}`,
        error: error.message,
        responseTime: Date.now(),
        statusCode: error.response?.status,
      });

      return createErrorResponse(
        `Error searching for products: ${error.message}`,
        {
          topic,
          statusCode: error.response?.status,
        }
      );
    }
  }
);

/**
 * Tool: Get Product Details
 * Fetches detailed information about a specific Amazon product using its ASIN
 */
server.registerTool(
  "get-product-details",
  {
    title: "Get Amazon Product Details",
    description:
      "Fetch detailed information about an Amazon product using its ASIN",
    inputSchema: {
      asin: z
        .string()
        .describe(
          "Amazon Standard Identification Number (ASIN) of the product"
        ),
    },
  },
  async ({ asin }) => {
    try {
      logger.logScrapingAttempt({
        topic: `Product details for ${asin}`,
        url: `${AMAZON_BASE_URL}/dp/${asin}`,
      });

      const productUrl = `${AMAZON_BASE_URL}/dp/${asin}`;

      // Get user agent and proxy configuration
      const userAgent = userAgentService.getRotatedUserAgent();
      const proxyConfig = proxyService.isEnabled()
        ? await proxyService.getProxyConfig()
        : null;

      const axiosConfig: any = {
        headers: {
          "User-Agent": userAgent,
        },
        timeout: 30000,
      };

      if (proxyConfig) {
        axiosConfig.proxy = proxyConfig;
      }

      const response = await axios.get<string>(productUrl, axiosConfig);

      logger.logScrapingSuccess({
        topic: `Product details for ${asin}`,
        url: productUrl,
        statusCode: response.status,
        responseTime: Date.now(),
        userAgent,
        proxyUrl: proxyConfig?.host,
      });

      // Parse HTML with Cheerio
      const $ = cheerio.load(response.data);

      // Extract product details
      const title = $("#productTitle").text().trim();
      const priceWhole = $(".a-price-whole").first().text().trim();
      const priceFraction = $(".a-price-fraction").first().text().trim();
      const priceString = priceWhole + priceFraction;
      const price = priceString
        ? parseFloat(priceString.replace(/[^\d.]/g, ""))
        : null;
      const currency = $(".a-price-symbol").first().text().trim();
      const rating =
        $(".a-icon-alt")
          .first()
          .text()
          .match(/[\d.]+/)?.[0] || "";
      const reviewCount =
        $(".a-size-base")
          .filter((_, el) => $(el).text().includes("ratings"))
          .text()
          .match(/[\d,]+/)?.[0] || "";

      // Extract main product image
      const mainImage =
        $("#landingImage").attr("src") ||
        $(".a-dynamic-image").first().attr("src") ||
        "";

      // Extract product features/bullets
      const features: string[] = [];
      $("#feature-bullets ul li span").each((_, element) => {
        const feature = $(element).text().trim();
        if (feature && !feature.includes("Make sure") && feature.length > 10) {
          features.push(feature);
        }
      });

      // Save/update product details in database
      await prisma.product.upsert({
        where: { asin },
        update: {
          title: title || undefined,
          price: price || undefined,
          currency: currency || undefined,
          mainImageUrl: mainImage || undefined,
          description: features.length > 0 ? features.join("\n") : undefined,
        },
        create: {
          asin,
          title: title || undefined,
          price: price || undefined,
          currency: currency || undefined,
          mainImageUrl: mainImage || undefined,
          description: features.length > 0 ? features.join("\n") : undefined,
        },
      });

      const productDetailsResult: ProductDetailsResult = {
        success: true,
        asin,
        product: {
          title: title || "Not found",
          price: {
            amount: price,
            currency: currency || "",
            formatted: price ? `${currency} ${price}` : "Not available",
          },
          rating: {
            value: rating || "",
            formatted: rating ? `${rating}/5` : "Not available",
          },
          reviews: {
            count: reviewCount || "",
            formatted: reviewCount || "Not available",
          },
          images: {
            main: mainImage || "",
          },
          features,
          url: productUrl,
        },
      };

      return createSuccessResponse(productDetailsResult);
    } catch (error: any) {
      logger.logScrapingFailure({
        topic: `Product details for ${asin}`,
        url: `${AMAZON_BASE_URL}/dp/${asin}`,
        error: error.message,
        responseTime: Date.now(),
        statusCode: error.response?.status,
      });

      return createErrorResponse(
        `Error fetching product details for ASIN ${asin}: ${error.message}`,
        {
          asin,
          statusCode: error.response?.status,
        }
      );
    }
  }
);

/**
 * Tool: Fetch Product Image
 * Downloads and returns a product image from Amazon
 */
server.registerTool(
  "fetch-product-image",
  {
    title: "Fetch Product Image",
    description: "Download and return a product image from Amazon",
    inputSchema: {
      asin: z
        .string()
        .describe(
          "Amazon Standard Identification Number (ASIN) of the product"
        ),
      imageType: z
        .enum(["main", "thumbnail"])
        .default("main")
        .describe("Type of image to fetch"),
    },
  },
  async ({ asin, imageType = "main" }) => {
    try {
      // First get the product details to find the image URL
      const product = await prisma.product.findUnique({
        where: { asin },
      });

      if (!product || !product.mainImageUrl) {
        return createErrorResponse(
          `No image URL found for ASIN ${asin}. Try running "get-product-details" first to fetch product information.`,
          {
            asin,
            imageType,
          }
        );
      }

      const imageUrl = product.mainImageUrl;

      // Get user agent and proxy configuration
      const userAgent = userAgentService.getRotatedUserAgent();
      const proxyConfig = proxyService.isEnabled()
        ? await proxyService.getProxyConfig()
        : null;

      const axiosConfig: any = {
        headers: {
          "User-Agent": userAgent,
        },
        timeout: 30000,
        responseType: "arraybuffer",
      };

      if (proxyConfig) {
        axiosConfig.proxy = proxyConfig;
      }

      const response = await axios.get(imageUrl, axiosConfig);
      const base64Image = Buffer.from(response.data as ArrayBuffer).toString(
        "base64"
      );
      const mimeType = response.headers["content-type"] || "image/jpeg";

      const imageFetchResult: ImageFetchResult = {
        success: true,
        asin,
        imageType,
        message: `Successfully fetched ${imageType} image for ASIN ${asin}`,
      };

      return {
        content: [
          {
            type: "object" as const,
            properties: imageFetchResult,
          },
          {
            type: "image" as const,
            data: base64Image,
            mimeType: mimeType,
          },
        ],
      };
    } catch (error: any) {
      return createErrorResponse(
        `Error fetching image for ASIN ${asin}: ${error.message}`,
        {
          asin,
          imageType,
        }
      );
    }
  }
);

/**
 * Resource: Search History
 * Provides access to recent search queries
 */
server.registerResource(
  "search-history",
  "search://history",
  {
    title: "Search History",
    description: "Recent Amazon product search queries",
    mimeType: "application/json",
  },
  async (uri) => {
    try {
      const recentSearches = await prisma.searchQuery.findMany({
        take: 20,
        orderBy: { createdAt: "desc" },
        include: {
          products: {
            select: { asin: true },
          },
        },
      });

      const searchHistory = {
        success: true,
        totalSearches: recentSearches.length,
        searches: recentSearches.map((search) => ({
          id: search.id,
          topic: search.topic,
          limit: search.limit,
          productCount: search.products.length,
          createdAt: search.createdAt,
          products: search.products.map((p) => p.asin),
        })),
      };

      return {
        contents: [
          {
            uri: uri.href,
            text: JSON.stringify(searchHistory, null, 2),
            mimeType: "application/json",
          },
        ],
      };
    } catch (error: any) {
      return {
        contents: [
          {
            uri: uri.href,
            text: JSON.stringify({ error: error.message }, null, 2),
            mimeType: "application/json",
          },
        ],
      };
    }
  }
);

/**
 * Resource: Product Database
 * Provides access to stored product information
 */
server.registerResource(
  "product-database",
  "products://database",
  {
    title: "Product Database",
    description: "All stored Amazon product information",
    mimeType: "application/json",
  },
  async (uri) => {
    try {
      const products = await prisma.product.findMany({
        take: 100,
        orderBy: { updatedAt: "desc" },
      });

      const productDatabase = {
        success: true,
        totalProducts: products.length,
        products: products.map((product) => ({
          asin: product.asin,
          title: product.title,
          price: product.price,
          currency: product.currency,
          mainImageUrl: product.mainImageUrl,
          description: product.description,
          createdAt: product.createdAt,
          updatedAt: product.updatedAt,
        })),
      };

      return {
        contents: [
          {
            uri: uri.href,
            text: JSON.stringify(productDatabase, null, 2),
            mimeType: "application/json",
          },
        ],
      };
    } catch (error: any) {
      return {
        contents: [
          {
            uri: uri.href,
            text: JSON.stringify({ error: error.message }, null, 2),
            mimeType: "application/json",
          },
        ],
      };
    }
  }
);

/**
 * Main function to start the MCP server
 */
async function main() {
  try {
    // Connect to stdio transport for MCP communication
    const transport = new StdioServerTransport();
    await server.connect(transport);

    logger.info("Amazon Scraper MCP Server started successfully");
    // Removed console.error to avoid interfering with MCP stdio communication
  } catch (error) {
    logger.error("Failed to start MCP server:", error as Error);
    // Removed console.error to avoid interfering with MCP stdio communication
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on("SIGINT", async () => {
  // Removed console.error to avoid interfering with MCP stdio communication
  await prisma.$disconnect();
  process.exit(0);
});

process.on("SIGTERM", async () => {
  // Removed console.error to avoid interfering with MCP stdio communication
  await prisma.$disconnect();
  process.exit(0);
});

// Start the server
if (require.main === module) {
  main().catch((_error) => {
    // Removed console.error to avoid interfering with MCP stdio communication
    process.exit(1);
  });
}

export default server;
