# Amazon Scraper MCP Server

This is a Model Context Protocol (MCP) server that provides Amazon product scraping capabilities to AI assistants and other MCP clients.

## Features

### Tools
- **search-amazon-products**: Search for products on Amazon.sg and return ASINs
- **get-product-details**: Fetch detailed information about a specific product using its ASIN
- **fetch-product-image**: Download and return product images

### Resources
- **search-history**: Access to recent search queries
- **product-database**: All stored Amazon product information

## Installation

1. Install dependencies:
```bash
npm install
```

2. Set up the database:
```bash
npm run prisma:generate
npm run prisma:migrate
```

3. Build the project:
```bash
npm run build
```

## Usage

### Running the MCP Server

#### Development Mode
```bash
npm run mcp
```

#### Production Mode
```bash
npm run mcp:build
```

### Connecting to MCP Clients

#### Claude Desktop
Add this configuration to your Claude Desktop MCP settings:

```json
{
  "mcpServers": {
    "amazon-scraper": {
      "command": "node",
      "args": ["./build/mcp-server.js"],
      "cwd": "/path/to/your/mcp-server",
      "env": {
        "NODE_ENV": "production"
      }
    }
  }
}
```

#### Other MCP Clients
The server uses stdio transport, so it can be connected to any MCP client that supports this transport method.

## Available Tools

### 1. search-amazon-products
Search for products on Amazon.sg.

**Parameters:**
- `topic` (string): Search query/topic for products
- `limit` (number, optional): Maximum number of products to return (1-50, default: 10)

**Example:**
```
Search for "wireless headphones" with a limit of 5 products
```

### 2. get-product-details
Fetch detailed information about a specific Amazon product.

**Parameters:**
- `asin` (string): Amazon Standard Identification Number (ASIN) of the product

**Example:**
```
Get details for ASIN "B08N5WRWNW"
```

### 3. fetch-product-image
Download and return a product image.

**Parameters:**
- `asin` (string): Amazon Standard Identification Number (ASIN) of the product
- `imageType` (enum, optional): Type of image to fetch ("main" or "thumbnail", default: "main")

**Example:**
```
Fetch main image for ASIN "B08N5WRWNW"
```

## Available Resources

### 1. search://history
Provides access to the 20 most recent search queries with their results.

### 2. products://database
Provides access to all stored product information (up to 100 most recently scraped products).

## Environment Variables

The MCP server uses the same environment variables as the main Express server:

- Database configuration (Prisma)
- Proxy settings (if enabled)
- User agent rotation settings

## Error Handling

The server includes comprehensive error handling and logging:
- All scraping attempts are logged
- Failed requests are tracked with error details
- Database operations are wrapped in try-catch blocks
- Graceful shutdown handling for SIGINT and SIGTERM

## Development

### Testing the MCP Server
You can test the MCP server using the MCP TypeScript SDK client or any other MCP client.

### Adding New Tools
To add new tools, extend the `mcp-server.ts` file with additional `server.registerTool()` calls.

### Adding New Resources
To add new resources, extend the `mcp-server.ts` file with additional `server.registerResource()` calls.

## Troubleshooting

### Common Issues

1. **Database Connection Errors**: Ensure Prisma is properly configured and migrations are run
2. **Network Errors**: Check proxy settings and user agent configuration
3. **Permission Errors**: Ensure the server has proper file system permissions

### Logs
The server uses the same logging system as the main Express server. Check the logs for detailed error information.

## License

This project is licensed under the ISC License.
