{"name": "mcp-server", "version": "1.0.0", "main": "index.js", "bin": {"amazon-scraper-mcp": "./build/mcp-server.js"}, "scripts": {"build": "tsc", "dev": "ts-node-dev --respawn --transpile-only src/server.ts", "start": "node build/server.js", "mcp": "ts-node src/mcp-server.ts", "mcp:build": "tsc && node build/mcp-server.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "prisma:reset": "prisma migrate reset", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "docker:build": "docker build -t mcp-server .", "docker:run": "docker-compose up", "docker:dev": "docker-compose --profile dev up", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f mcp-server"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@modelcontextprotocol/sdk": "^1.16.0", "@prisma/client": "^6.12.0", "axios": "^1.11.0", "cheerio": "^1.1.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "prisma": "^6.12.0", "puppeteer": "^24.15.0", "zod": "^3.25.76"}, "devDependencies": {"@types/axios": "^0.9.36", "@types/cheerio": "^0.22.35", "@types/cors": "^2.8.19", "@types/dotenv": "^6.1.1", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.1.0", "@types/supertest": "^6.0.3", "jest": "^30.0.5", "supertest": "^7.1.4", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}